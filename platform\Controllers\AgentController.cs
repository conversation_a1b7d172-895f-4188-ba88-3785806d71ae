﻿using Amazon.BedrockAgent;
using Amazon.BedrockAgent.Model;
using Amazon.DynamoDBv2.DataModel;
using Amazon.DynamoDBv2.DocumentModel;
using Amazon.Runtime.Internal;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using platform.Components.Core;
using platform.Constants;
using platform.Models.Configuration;
using platform.Models.Document;
using platform.Models.Document.AWS;
using platform.Models.Enum;
using platform.Models.Request;
using platform.Models.Response;
using platform.Services;
using shared.Components.ApiEventBus;
using shared.Controllers;
using shared.Converters;
using shared.Extensions;
using shared.Models.Document.AWS;
using shared.Models.Response;
using System.Reflection;

namespace platform.Controllers
{
    [Route(Routes.AgentController.BasePath)]
    [Produces("application/json")]
    public class AgentController : SuperController
    {
        private readonly ILogger<AgentController> logger;
        private readonly IAmazonBedrockAgent bedrockAgent;
        private readonly IOptionsMonitor<BedrockConfiguration> bedrockConfiguration;
        private readonly IIAM iAM;

        public AgentController(ILogger<AgentController> logger, IApiEventBus apiEventBus, IDynamoDBContext dynamoDBContext, IAmazonBedrockAgent bedrockAgent, IIAM iAM, IOptionsMonitor<BedrockConfiguration> bedrockConfiguration) : base(apiEventBus, dynamoDBContext)
        {
            this.logger = logger;
            this.bedrockAgent = bedrockAgent;
            this.bedrockConfiguration = bedrockConfiguration;
            this.iAM = iAM;
        }

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpPost]
        public async Task<IActionResult> CreateAgent([FromBody] AgentCreateRequest request)
        {
            int delayInSeconds = 0;
            string id = Guid.NewGuid().ToString();

            AwsAgent awsAgent = new AwsAgent();
            awsAgent.Name = request.Name;
            awsAgent.Description = request.Description;
            awsAgent.Status = Models.Enum.AgentStatus.QUEUED;
            awsAgent.AgentId = id;
            awsAgent.AccountId = GetAccountId();
            awsAgent.DefaultPrompt = request.DefaultPrompt;

            var savedAgent = await PutDBEntry<AwsAgent>(awsAgent);
            if(savedAgent != null)  savedAgent.Status = Models.Enum.AgentStatus.DB_ENTRY_CREATED;

            if (await this.DispatchApiEvent(
                savedAgent ?? awsAgent,
                shared.Models.Enum.MicroserviceType.Platform,
                Constants.Routes.AgentController.BasePath,
                Constants.Routes.AgentController.Internal.CREATE_PROCESS,
                delayInSeconds))

                return Ok();
            return StatusCode(500, "Internal error creating agent");
        }

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpPost(Constants.Routes.KnowledgeBaseController.Public.ASSIGN_KNOWLEDGEBASE)]
        public async Task<IActionResult> AssignKnowledgeBase([FromBody] AgentAssignKbRequest request)
        {
            string accountId = GetAccountId();

            var knowledgeBase = await GetDBEntry<AwsKnowledgeBase>(accountId, request.KnowledgebaseId);
            var agent = await GetDBEntry<AwsAgent>(accountId, request.AgentId);

            if(knowledgeBase == null ||  agent == null)
            {
                return BadRequest("Invalid Agent or Knowledgebase.");
            }

            var awsRequest = new AssociateAgentKnowledgeBaseRequest();
            awsRequest.AgentId = agent.AwsData.AgentId;
            awsRequest.KnowledgeBaseId = knowledgeBase.AwsData.AwsKbId;
            awsRequest.Description = request.Description;
            awsRequest.KnowledgeBaseState = "ENABLED";
            awsRequest.AgentVersion = "DRAFT";
            var resp = await bedrockAgent.AssociateAgentKnowledgeBaseAsync(awsRequest);

            return Ok();

        }


        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.MicroserviceAuthScheme)]
        [HttpPost(Constants.Routes.AgentController.Internal.CREATE_PROCESS)]
        public async Task<IActionResult> CreateAgentProcess([FromBody] AwsAgent agent)
        {
            int delayInSeconds = 0;
            switch (agent.Status)
            {
                case Models.Enum.AgentStatus.QUEUED:
                    if (await PutDBEntry<AwsAgent>(agent) != null)
                    {
                        delayInSeconds = 0;
                        agent.Status = Models.Enum.AgentStatus.DB_ENTRY_CREATED;
                    }
                    else
                    {
                        delayInSeconds = 5;
                    }
                    break;
                case Models.Enum.AgentStatus.DB_ENTRY_CREATED:
                    {
                        var createAgentRequest = new Amazon.BedrockAgent.Model.CreateAgentRequest();
                        createAgentRequest.AgentName = agent.AgentId;
                        createAgentRequest.Description = agent.Description;
                        createAgentRequest.AgentResourceRoleArn = $"arn:aws:iam::{await iAM.GetProviderAccountNumber()}:role/service-role/{bedrockConfiguration.CurrentValue.AgentRoleName}";
                        createAgentRequest.FoundationModel = agent.LLMModelType.DisplayName();
                        createAgentRequest.Instruction = agent.DefaultPrompt;
                        createAgentRequest.ClientToken = agent.AgentId;

                        var resp = await bedrockAgent.CreateAgentAsync(createAgentRequest);

                        agent.AwsData.AgentId = resp.Agent.AgentId;
                        agent.Status = Models.Enum.AgentStatus.CREATING;
                        await PutDBEntry<AwsAgent>(agent);
                    }
                    break;
                case Models.Enum.AgentStatus.CREATING:
                    {
                        var getAgentRequest = new Amazon.BedrockAgent.Model.GetAgentRequest();
                        getAgentRequest.AgentId = agent.AwsData.AgentId;
                        var resp = await bedrockAgent.GetAgentAsync(getAgentRequest);

                        if (resp.Agent.AgentStatus == Amazon.BedrockAgent.AgentStatus.CREATING)
                        {
                            delayInSeconds = 10;
                        }
                        else if (resp.Agent.AgentStatus == Amazon.BedrockAgent.AgentStatus.NOT_PREPARED)
                        {
                            agent.Status = Models.Enum.AgentStatus.READY;
                            await PutDBEntry<AwsAgent>(agent);
                        }
                    }
                    break;
                case Models.Enum.AgentStatus.READY:
                    {
                        var agentCheck = await dBContext.LoadAsync<AwsAgent>(GetAccountId(), agent.AgentId);
                        if (agentCheck.Status != agent.Status && await PutDBEntry<AwsAgent>(agent) == null)
                        {
                            delayInSeconds = 5;
                        }
                        else
                        {
                            return Ok();
                        }
                    }
                    break;
            }


            if (!await this.DispatchApiEvent(
                agent, 
                shared.Models.Enum.MicroserviceType.Platform,
                Constants.Routes.AgentController.BasePath, 
                Constants.Routes.AgentController.Internal.CREATE_PROCESS, 
                delayInSeconds))
            {
                return BadRequest("error 500");
            }

            return Ok();
        }

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpPost(Constants.Routes.AgentController.Public.SET_ALIAS)]
        public async Task<IActionResult> PutAliasRequest([FromBody] AgentPutAliasRequest request)
        {
            string accountId = GetAccountId();

            AwsAgentTag agentTag = await GetDBEntry<AwsAgentTag>(request.AgentId, request.AgentTag);
            if (agentTag == null || agentTag.AccountId != accountId) return NotFound("Agent not found");


            AwsAgentAlias alias = await GetDBEntry<AwsAgentAlias>(request.AgentId, request.Alias);
            if (alias != null)
            {
                //update aws

                var awsResp = await bedrockAgent.UpdateAgentAliasAsync(
                    new UpdateAgentAliasRequest() { 
                        AgentAliasId = agentTag.AwsData.AliasId,
                        AgentAliasName = agentTag.AwsData.AliasName,
                        AgentId = agentTag.AwsData.AgentId,
                        Description = request.Alias,
                        RoutingConfiguration = new List<AgentAliasRoutingConfigurationListItem>() { 
                            new AgentAliasRoutingConfigurationListItem() { AgentVersion = agentTag.AwsData.Version } } });

                alias.AwsData.Version = alias.AwsData.Version = agentTag.AwsData.Version;
                alias.AwsData.AliasId = awsResp.AgentAlias.AgentAliasId;
            }
            else 
            {
                alias = new AwsAgentAlias();

                string awsAliasNameId = Guid.NewGuid().ToString();
                alias = new AwsAgentAlias();

                alias.Alias = request.Alias;
                alias.AgentId = request.AgentId;
                alias.AgentTag = request.AgentTag;
                alias.AccountId = accountId;
                alias.Description = request.Description;
                alias.AwsData.AgentId = agentTag.AwsData.AgentId;
                alias.AwsData.AliasName = awsAliasNameId;


                CreateAgentAliasRequest awsReq = new CreateAgentAliasRequest();
                awsReq.AgentId = agentTag.AwsData.AgentId;
                awsReq.AgentAliasName = request.Alias;
                awsReq.Description = request.Description;
                awsReq.RoutingConfiguration = new List<AgentAliasRoutingConfigurationListItem>() { new AgentAliasRoutingConfigurationListItem() { AgentVersion = agentTag.AwsData.Version } };
                alias.AwsData.Version = agentTag.AwsData.Version;

                var awsResp = await bedrockAgent.CreateAgentAliasAsync(awsReq);

                
                alias.AwsData.AliasId = awsResp.AgentAlias.AgentAliasId;
            }



            if (await PutDBEntry(alias) == null)
            {
                if (!await this.DispatchApiEvent(
                alias,
                shared.Models.Enum.MicroserviceType.Platform,
                Constants.Routes.AgentController.BasePath,
                Constants.Routes.AgentController.Internal.ALIAS_PROCESS,
                0))
                {
                    return BadRequest("error 500");
                }
                return Ok();
            }
            return Ok();
        }


        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.MicroserviceAuthScheme)]
        [HttpPost(Constants.Routes.AgentController.Internal.ALIAS_PROCESS)]
        public async Task<IActionResult> PutAliasProcess(AwsAgentAlias agentAlias)
        {
            if (await PutDBEntry(agentAlias) == null)
            {
                return StatusCode(500, "Failed to update DynamoDB");
            }
            return Ok();
        }



        public class AgentDeployProcessRequest
        {
            public AgentDeployRequest Request { get; set; } = new AgentDeployRequest();
            public AwsAgent Agent { get; set; } = new AwsAgent();
            public AwsAgentTag AgentTag { get; set; } = new AwsAgentTag();
        }

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpPost(Constants.Routes.AgentController.Public.DEPLOY)]
        public async Task<IActionResult> DeployAgent([FromRoute] string agentId,[FromBody] AgentDeployRequest request)
        {
            string accountId = GetAccountId();
            AwsAgent agent = await GetDBEntry<AwsAgent>(accountId, agentId);
            if (agent == null) return BadRequest("Agent not found");

            AwsAgentTag awsAgentTag = await GetDBEntry<AwsAgentTag>(agentId, request.Tag);
            if (awsAgentTag != null) return BadRequest("Tag already exists");

            var setAgent = await SetDBEntryStatusAtomic(agent, Models.Enum.AgentStatus.SEALING, Models.Enum.AgentStatus.READY);
            if (setAgent == null) return BadRequest("Can only deploy an agent that is in READY state.");

            var internalReq = new AgentDeployProcessRequest();
            internalReq.Request = request;
            internalReq.Agent = setAgent;

            if (!await this.DispatchApiEvent(
                internalReq,
                shared.Models.Enum.MicroserviceType.Platform,
                Constants.Routes.AgentController.BasePath,
                Constants.Routes.AgentController.Internal.DEPLOY_PROCESS,
                0))
            {
                return BadRequest("error 500");
            }

            return Ok();
        }


        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.MicroserviceAuthScheme)]
        [HttpPost(Constants.Routes.AgentController.Internal.DEPLOY_PROCESS)]
        public async Task<IActionResult> DeployAgentProcess([FromBody] AgentDeployProcessRequest processRequest) {

            int delayInSeconds = 5;

            switch (processRequest.Agent.Status)
            {
                case Models.Enum.AgentStatus.SEALING:
                    {
                        PrepareAgentRequest preReq = new PrepareAgentRequest();
                        preReq.AgentId = processRequest.Agent.AwsData.AgentId;
                        var resp = await bedrockAgent.PrepareAgentAsync(preReq);

                        processRequest.Agent.Status = Models.Enum.AgentStatus.PREPARING;
                        await PutDBEntry(processRequest.Agent);
                        delayInSeconds = 0;
                    }
                    break;
                case Models.Enum.AgentStatus.PREPARING:
                    {
                        var agentReq = new GetAgentRequest();
                        agentReq.AgentId = processRequest.Agent.AwsData.AgentId;
                        var resp = await bedrockAgent.GetAgentAsync(agentReq);

                        if(resp.Agent.AgentStatus == Amazon.BedrockAgent.AgentStatus.PREPARED)
                        {
                            delayInSeconds = 0;
                            processRequest.Agent.Status = Models.Enum.AgentStatus.VERSIONING;
                            await PutDBEntry(processRequest.Agent);
                        }
                        else
                        {
                            delayInSeconds = 10;
                        }
                    }
                    break;
                case Models.Enum.AgentStatus.VERSIONING:
                    {
                        string awsAliasName = Guid.NewGuid().ToString();

                        CreateAgentAliasRequest awsReq = new CreateAgentAliasRequest();
                        awsReq.AgentId = processRequest.Agent.AwsData.AgentId;
                        awsReq.AgentAliasName = awsAliasName;
                        awsReq.Description = processRequest.Request.Description;
                        awsReq.ClientToken = processRequest.Agent.AgentId + "-" + shared.Helpers.EncodingHelper.MD5(processRequest.Request.Tag);

                        var awsResp = await bedrockAgent.CreateAgentAliasAsync(awsReq);

                        string accountId = GetAccountId();

                        processRequest.AgentTag = new AwsAgentTag();
                        processRequest.AgentTag.Tag = processRequest.Request.Tag;
                        processRequest.AgentTag.AccountId = accountId;
                        processRequest.AgentTag.AgentId = processRequest.Agent.AgentId;
                        processRequest.AgentTag.AwsData.AgentId = processRequest.Agent.AwsData.AgentId;
                        processRequest.AgentTag.AwsData.AliasId = awsResp.AgentAlias.AgentAliasId;
                        processRequest.AgentTag.AwsData.Version = string.Empty;
                        processRequest.AgentTag.AwsData.AliasName = awsAliasName;                    

                        processRequest.Agent.Status = Models.Enum.AgentStatus.WAITING_VERSION;
                        await PutDBEntry(processRequest.Agent);
                    }
                    break;
                case Models.Enum.AgentStatus.WAITING_VERSION:
                    {
                        var awsResp = await bedrockAgent.GetAgentAliasAsync(new GetAgentAliasRequest() { 
                            AgentAliasId = processRequest.AgentTag.AwsData.AliasId , 
                            AgentId = processRequest.Agent.AwsData.AgentId });

                        if (awsResp.AgentAlias.RoutingConfiguration[0].AgentVersion == null)
                        {
                            delayInSeconds = 5;
                        }
                        else
                        {
                            processRequest.Agent.Status = Models.Enum.AgentStatus.TAGGING;
                            delayInSeconds = 0;
                            processRequest.AgentTag.AwsData.Version = awsResp.AgentAlias.RoutingConfiguration[0].AgentVersion;
                            await PutDBEntry(processRequest.Agent);
                        }
                    }
                    break;
                case Models.Enum.AgentStatus.TAGGING:
                    {
                        if(await PutDBEntry(processRequest.AgentTag) == null)
                        {
                            delayInSeconds = 10;
                        }
                        else
                        {
                            processRequest.Agent.Status = Models.Enum.AgentStatus.READY;
                            if(await PutDBEntry(processRequest.Agent) == null)
                            {
                                processRequest.Agent.Status = Models.Enum.AgentStatus.UPDATING;
                                delayInSeconds = 10;
                            }
                            else
                            {
                                return Ok();
                            }
                        }
                    }
                    break;
                case Models.Enum.AgentStatus.UPDATING:
                    {
                        processRequest.Agent.Status = Models.Enum.AgentStatus.READY;
                        if (await PutDBEntry(processRequest.Agent) == null)
                        {
                            processRequest.Agent.Status = Models.Enum.AgentStatus.UPDATING;
                            delayInSeconds = 10;
                        }
                        else
                        {
                            return Ok();
                        }
                    }
                    break;
            }


            if (!await this.DispatchApiEvent(
                processRequest,
                shared.Models.Enum.MicroserviceType.Platform,
                Constants.Routes.AgentController.BasePath,
                Constants.Routes.AgentController.Internal.DEPLOY_PROCESS,
                delayInSeconds))
            {
                return BadRequest("error 500");
            }
            return Ok();

        }


        private async Task<List<AwsAgentTag>> GetTagByAwsVersion(AwsAgent agent)
        {
            var enumConvert = new DynamoEnumStringConverter<KnowledgeBaseFileUploadStatus>();
            var queryConf = new QueryOperationConfig();

            queryConf.Limit = 1;
            queryConf.FilterExpression = new Expression();
            queryConf.FilterExpression.ExpressionAttributeNames = new Dictionary<string, string>() { { "#V", nameof(AwsAgentTag.AwsData) + "." + nameof(AwsAgentTag.AwsData.Version) } };
            queryConf.FilterExpression.ExpressionAttributeValues = new Dictionary<string, DynamoDBEntry> { { ":v", agent.AwsData.VersionCounter } };
            queryConf.FilterExpression.ExpressionStatement = "#V = :v";
            queryConf.KeyExpression = new Amazon.DynamoDBv2.DocumentModel.Expression();
            queryConf.KeyExpression.ExpressionStatement = "#A = :a";
            queryConf.KeyExpression.ExpressionAttributeNames = new Dictionary<string, string>() { { "#A", nameof(AwsAgentTag.AgentId) } };
            queryConf.KeyExpression.ExpressionAttributeValues = new Dictionary<string, DynamoDBEntry> { { ":a", new Primitive(agent.AgentId) } };
            queryConf.Select = SelectValues.AllAttributes;

            var query = dBContext.FromQueryAsync<AwsAgentTag>(queryConf);
            var pendingFiles = await query.GetNextSetAsync();

            return pendingFiles ?? new List<AwsAgentTag>();
        }


        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpGet(Constants.Routes.AgentController.Public.SEARCH)]
        public async Task<IActionResult> SearchAgent([FromQuery] string query, [FromQuery] int limit = 6)
        {
            if (limit > 6) limit = 6;
            if (limit < 1) limit = 1;

            string accountId = GetAccountId();
            List<string> propertiesToGet = typeof(AgentResponse).GetProperties().Select((PropertyInfo p) => p.Name).ToList();

            ListResponse<Models.Document.Agent> agents = await SearchDBEntries<Models.Document.Agent>(nameof(Models.Document.Agent.AccountId), accountId, query, limit, attributesToGet: propertiesToGet);

            var loggerFactory = HttpContext.RequestServices.GetRequiredService<ILoggerFactory>();
            var mapper = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<Models.Document.Agent, AgentResponse>();
            }, loggerFactory).CreateMapper();


            var result = new ListResponse<AgentResponse>()
            {
                Entries = mapper.Map<IList<Models.Document.Agent>, IList<AgentResponse>>(agents.Entries).ToList(),
                NextToken = agents.NextToken,
                Total = agents.Total,
            };

            return Ok(result);
        }

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpGet(Constants.Routes.AgentController.Public.LIST)]
        public async Task<IActionResult> ListAgents([FromQuery] int count = 0, [FromQuery] string? nextToken = null)
        {
            if (count < 10) count = 10;
            if (count > 100) count = 100;


            var listEntriesInternal = await GetDBEntries<AwsAgent>(nameof(AwsAgent.AccountId), GetAccountId(), count, nextToken, getTotal: nextToken == null);

            var loggerFactory = HttpContext.RequestServices.GetRequiredService<ILoggerFactory>();
            var mapper = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<AwsAgent, AgentResponse>();
            }, loggerFactory).CreateMapper();
             

            var result = new ListResponse<AgentResponse>()
            {
                Entries = mapper.Map<IList<AwsAgent>, IList<AgentResponse>>(listEntriesInternal.Entries).ToList(),
                NextToken = listEntriesInternal.NextToken,
                Total = listEntriesInternal.Total,
            };

            return Ok(result);
        }

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpPut(Constants.Routes.AgentController.Public.PUT)]
        public async Task<IActionResult> PutAgent([FromRoute] string agentId, [FromBody] AgentPutRequest agentPutRequest)
        {
            string accountId = GetAccountId();
            AwsAgent agent = await GetDBEntry<AwsAgent>(accountId, agentId);
            if (agent == null) return BadRequest("Not found");

            agent.Name = agentPutRequest.Name;
            agent.Description = agentPutRequest.Description;
            agent.DefaultPrompt = agentPutRequest.DefaultPrompt;

            if (await UpdateDBEntry<AwsAgent>(agent, new List<string>() { nameof(AwsAgent.Name), nameof(AwsAgent.Description), nameof(AwsAgent.DefaultPrompt) }) == null)
            {
                return StatusCode(500, "Couldn't update");
            }
            return Ok(agent);
        }

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpGet(Constants.Routes.AgentController.Public.GET)]
        public async Task<IActionResult> GetAgent([FromRoute] string agentId)
        {
            string accountId = GetAccountId();
            AwsAgent agent = await GetDBEntry<AwsAgent>(accountId, agentId);
            if (agent == null) return BadRequest("Not found");

            var loggerFactory = HttpContext.RequestServices.GetRequiredService<ILoggerFactory>();
            var mapper = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<AwsAgent, AgentResponse>();
            }, loggerFactory).CreateMapper();

            return Ok(mapper.Map<AwsAgent, AgentResponse>(agent));
        }


        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpGet(Constants.Routes.AgentController.Public.GET_KNOWLEDGE_BASES_FOR_AGENT)]
        public async Task<IActionResult> GetAgentsForKnowledgeBase(string agentId, [FromQuery] string? nextToken = null, [FromQuery] int count = -1)
        {
            string accountId = GetAccountId();
            ListResponse<Models.Document.AgentKnowledgeBase> response = await GetDBEntries<Models.Document.AgentKnowledgeBase>(
                nameof(Models.Document.AgentKnowledgeBase.AgentId),
                agentId,
                count,
                nextToken,
                filterExpression: new Amazon.DynamoDBv2.DocumentModel.Expression()
                {
                    ExpressionAttributeNames = new Dictionary<string, string>() { { $"#{nameof(Models.Document.AgentKnowledgeBase.AccountId)}", nameof(Models.Document.AgentKnowledgeBase.AccountId) } },
                    ExpressionAttributeValues = new Dictionary<string, Amazon.DynamoDBv2.DocumentModel.DynamoDBEntry> { { $":{nameof(Models.Document.AgentKnowledgeBase.AccountId)}", accountId } },
                    ExpressionStatement = $"#{nameof(Models.Document.AgentKnowledgeBase.AccountId)}=:{nameof(Models.Document.AgentKnowledgeBase.AccountId)}"
                },
                getTotal: false
            );

            if (response == null) return BadRequest("Not found");
            if (response.Entries.Count == 0)
            {
                return Ok(new ListResponse<AgentResponse>());
            }

            var attValues = new Dictionary<string, Amazon.DynamoDBv2.DocumentModel.DynamoDBEntry>();
            var statement = new List<string>();

            foreach (var item in response.Entries.Select((value, i) => new { i, value }))
            {
                if (item.value.AccountId != accountId) continue;
                attValues.Add($":kb{item.i}", item.value.KnowledgebaseId);
                statement.Add($"#{nameof(Models.Document.KnowledgeBase.KbId)}=:kb{item.i}");
            }

            ListResponse<Models.Document.KnowledgeBase> agents = await GetDBEntries<Models.Document.KnowledgeBase>(
                nameof(Models.Document.KnowledgeBase.AccountId),
                GetAccountId(),
                response.Entries.Count,
                filterExpression: new Amazon.DynamoDBv2.DocumentModel.Expression()
                {
                    ExpressionAttributeNames = new Dictionary<string, string>() { { $"#{nameof(Models.Document.KnowledgeBase.KbId)}", nameof(Models.Document.KnowledgeBase.KbId) } },
                    ExpressionAttributeValues = attValues,
                    ExpressionStatement = String.Join(" OR ", statement)
                },
                getTotal: false
            );

            var loggerFactory = HttpContext.RequestServices.GetRequiredService<ILoggerFactory>();
            var mapper = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<Models.Document.KnowledgeBase, KnowledgeBaseResponse>();
            }, loggerFactory).CreateMapper();

            var finalResponse = new ListResponse<KnowledgeBaseResponse>()
            {
                Entries = mapper.Map<IList<Models.Document.KnowledgeBase>, IList<KnowledgeBaseResponse>>(agents.Entries).ToList(),
                NextToken = agents.NextToken,
                Total = agents.Total,
            };

            return Ok(finalResponse);
        }


        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpGet(Constants.Routes.AgentController.Public.LIST_TAGS)]
        public async Task<IActionResult> ListAgentTags([FromRoute] string agentId, [FromQuery] int count = 0, [FromQuery] string? nextToken = null)
        {
            if (count < 10) count = 10;
            if (count > 100) count = 100;


            var listEntriesInternal = await GetDBEntries<AgentTag>(nameof(AgentTag.AgentId), agentId, count, nextToken, getTotal: nextToken == null, filterExpression: new Amazon.DynamoDBv2.DocumentModel.Expression()
            {
                ExpressionAttributeNames = new Dictionary<string, string>() { { $"#{nameof(AgentTag.AccountId)}", nameof(AgentTag.AccountId) } },
                ExpressionAttributeValues = new Dictionary<string, DynamoDBEntry> { { $":{nameof(AgentTag.AccountId)}", GetAccountId() } },
                ExpressionStatement = $"#{nameof(AgentTag.AccountId)}=:{nameof(AgentTag.AccountId)}"
            });

            var loggerFactory = HttpContext.RequestServices.GetRequiredService<ILoggerFactory>();
            var mapper = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<AgentTag, AgentTagResponse>();
            }, loggerFactory).CreateMapper();


            var result = new ListResponse<AgentTagResponse>()
            {
                Entries = mapper.Map<IList<AgentTag>, IList<AgentTagResponse>>(listEntriesInternal.Entries).ToList(),
                NextToken = listEntriesInternal.NextToken,
                Total = listEntriesInternal.Total,
            };

            return Ok(result);
        }

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpPost(Constants.Routes.AgentController.Public.CREATE_ALIAS)]
        public async Task<IActionResult> CreateAlias([FromRoute] string agentId, [FromBody] AgentAliasCreateRequest request)
        {
            string accountId = GetAccountId();

            AwsAgent agent = await GetDBEntry<AwsAgent>(accountId, agentId);
            if (agent == null) return NotFound("Agent not found");

            AwsAgentTag agentTag = await GetDBEntry<AwsAgentTag>(agentId, request.AgentTag);
            if (agentTag == null || agentTag.AccountId != accountId) return NotFound("Agent not found");            

            AwsAgentAlias alias = await GetDBEntry<AwsAgentAlias>(agentId, request.Alias);
            if (alias != null)
            {
                //update aws

                var awsResp = await bedrockAgent.UpdateAgentAliasAsync(
                    new UpdateAgentAliasRequest()
                    {
                        AgentAliasId = agentTag.AwsData.AliasId,
                        AgentAliasName = agentTag.AwsData.AliasName,
                        AgentId = agentTag.AwsData.AgentId,
                        Description = request.Alias,
                        RoutingConfiguration = new List<AgentAliasRoutingConfigurationListItem>() {
                            new AgentAliasRoutingConfigurationListItem() { AgentVersion = agentTag.AwsData.Version } }
                    });

                alias.AwsData.Version = alias.AwsData.Version = agentTag.AwsData.Version;
                alias.AwsData.AliasId = awsResp.AgentAlias.AgentAliasId;
            }
            else
            {
                alias = new AwsAgentAlias();

                string awsAliasNameId = Guid.NewGuid().ToString();
                alias = new AwsAgentAlias();

                alias.Alias = request.Alias;
                alias.AgentId = agentId;
                alias.AgentTag = request.AgentTag;
                alias.AccountId = accountId;
                alias.Description = request.Description;
                alias.AwsData.AgentId = agentTag.AwsData.AgentId;
                alias.AwsData.AliasName = awsAliasNameId;


                CreateAgentAliasRequest awsReq = new CreateAgentAliasRequest();
                awsReq.AgentId = agentTag.AwsData.AgentId;
                awsReq.AgentAliasName = request.Alias;
                awsReq.Description = request.Description;
                awsReq.RoutingConfiguration = new List<AgentAliasRoutingConfigurationListItem>() { new AgentAliasRoutingConfigurationListItem() { AgentVersion = agentTag.AwsData.Version } };
                alias.AwsData.Version = agentTag.AwsData.Version;

                var awsResp = await bedrockAgent.CreateAgentAliasAsync(awsReq);


                alias.AwsData.AliasId = awsResp.AgentAlias.AgentAliasId;
            }

            var loggerFactory = HttpContext.RequestServices.GetRequiredService<ILoggerFactory>();
            var mapper = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<AwsAgentAlias, AgentAliasResponse>();
            }, loggerFactory).CreateMapper();


            var resp = mapper.Map<AwsAgentAlias, AgentAliasResponse>(alias);
            resp.AgentName = agent.Name;

            if (await PutDBEntry(alias) == null)
            {
                if (!await this.DispatchApiEvent(
                alias,
                shared.Models.Enum.MicroserviceType.Platform,
                Constants.Routes.AgentController.BasePath,
                Constants.Routes.AgentController.Internal.ALIAS_PROCESS,
                0))
                {
                    return BadRequest("error 500");
                }
                return Ok();
            }
            return Ok(resp);
        }


        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpGet(Constants.Routes.AgentController.Public.LIST_ALIASES_FOR_AGENT)]
        public async Task<IActionResult> ListAliasesForAgent([FromRoute] string agentId, [FromQuery] int count = 0, [FromQuery] string? nextToken = null)
        {
            if (count < 10) count = 10;
            if (count > 100) count = 100;

            var accountId = GetAccountId();
            var listEntriesInternal = await GetDBEntries<Models.Document.AgentAlias>(
                nameof(Models.Document.AgentAlias.AgentId),
                agentId,
                count,
                nextToken,
                filterExpression: new Expression()
                {
                    ExpressionAttributeNames = new Dictionary<string, string>() { { $"#{nameof(Models.Document.AgentAlias.AccountId)}", nameof(Models.Document.AgentAlias.AccountId) } },
                    ExpressionAttributeValues = new Dictionary<string, DynamoDBEntry>() { { $":{nameof(Models.Document.AgentAlias.AccountId)}", accountId } },
                    ExpressionStatement = $"#{nameof(Models.Document.AgentAlias.AccountId)}=:{nameof(Models.Document.AgentAlias.AccountId)}"
                },
                getTotal: nextToken == null);

            var loggerFactory = HttpContext.RequestServices.GetRequiredService<ILoggerFactory>();
            var mapper = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<Models.Document.AgentAlias, AgentAliasResponse>();
            }, loggerFactory).CreateMapper();

            var result = new ListResponse<AgentAliasResponse>()
            {
                Entries = mapper.Map<IList<Models.Document.AgentAlias>, IList<AgentAliasResponse>>(listEntriesInternal.Entries).ToList(),
                NextToken = listEntriesInternal.NextToken,
                Total = listEntriesInternal.Total,
            };

            return Ok(result);
        }

    }
}
