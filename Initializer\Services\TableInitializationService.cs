using Amazon.DynamoDBv2;
using Initializer.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using shared.Models.Document;
using shared.Services;
using shared.Services.Implementation;
using System.Reflection;

namespace Initializer.Services
{
    /// <summary>
    /// Service for initializing and managing NoSQL tables using reflection and generic table managers.
    /// </summary>
    public class TableInitializationService : ITableInitializationService
    {
        private readonly IModelTypeDiscoveryService _modelDiscovery;
        private readonly IAmazonDynamoDB _dynamoClient;
        private readonly ILogger<TableInitializationService> _logger;
        private readonly TableManagementOptions _options;

        public TableInitializationService(
            IModelTypeDiscoveryService modelDiscovery,
            IAmazonDynamoDB dynamoClient,
            ILogger<TableInitializationService> logger,
            IOptions<TableManagementOptions> options)
        {
            _modelDiscovery = modelDiscovery;
            _dynamoClient = dynamoClient;
            _logger = logger;
            _options = options.Value;
        }

        /// <summary>
        /// Validates and initializes tables for all discovered model types.
        /// </summary>
        /// <returns>Task representing the operation with success status</returns>
        public async Task<bool> InitializeAllTablesAsync()
        {
            _logger.LogInformation("Starting initialization of all discovered tables");

            var modelTypes = _modelDiscovery.DiscoverModelTypes();
            return await InitializeTablesAsync(modelTypes);
        }

        /// <summary>
        /// Validates and initializes tables for specific model types.
        /// </summary>
        /// <param name="modelTypes">Model types to process</param>
        /// <returns>Task representing the operation with success status</returns>
        public async Task<bool> InitializeTablesAsync(IEnumerable<Type> modelTypes)
        {
            var modelTypesList = modelTypes.ToList();
            _logger.LogInformation("Starting initialization of {Count} tables", modelTypesList.Count);

            var allSuccessful = true;
            var tasks = modelTypesList.Select(async modelType =>
            {
                try
                {
                    var success = await InitializeTableAsync(modelType);
                    if (!success)
                    {
                        allSuccessful = false;
                    }
                    return success;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to initialize table for model type {ModelType}", modelType.Name);
                    allSuccessful = false;
                    return false;
                }
            });

            var results = await Task.WhenAll(tasks);
            var successCount = results.Count(r => r);

            _logger.LogInformation("Table initialization completed. {SuccessCount}/{TotalCount} tables initialized successfully",
                successCount, modelTypesList.Count);

            return allSuccessful;
        }

        /// <summary>
        /// Validates and initializes a table for a specific model type.
        /// </summary>
        /// <param name="modelType">Model type to process</param>
        /// <returns>Task representing the operation with success status</returns>
        public async Task<bool> InitializeTableAsync(Type modelType)
        {
            _logger.LogInformation("Initializing table for model type {ModelType}", modelType.Name);

            try
            {
                // Create table manager instance using reflection
                var tableManager = CreateTableManager(modelType);
                if (tableManager == null)
                {
                    _logger.LogError("Failed to create table manager for model type {ModelType}", modelType.Name);
                    return false;
                }

                // Get table name for logging
                var tableName = GetTableName(modelType);
                _logger.LogInformation("Processing table '{TableName}' for model type {ModelType}", tableName, modelType.Name);

                // Validate the table
                var validationResult = await ValidateTableUsingManager(tableManager);
                
                if (validationResult.IsValid)
                {
                    _logger.LogInformation("Table '{TableName}' validation passed", tableName);
                    return true;
                }

                _logger.LogWarning("Table '{TableName}' validation failed: {Errors}", 
                    tableName, string.Join(", ", validationResult.ValidationErrors));

                if (!_options.EnableTableRecreation)
                {
                    _logger.LogError("Table recreation is disabled. Table '{TableName}' remains invalid", tableName);
                    return false;
                }

                // Delete table if it exists
                var tableExists = await TableExistsUsingManager(tableManager);
                if (tableExists)
                {
                    _logger.LogInformation("Deleting existing table '{TableName}'", tableName);
                    var deleteSuccess = await DeleteTableUsingManager(tableManager);
                    if (!deleteSuccess)
                    {
                        _logger.LogError("Failed to delete table '{TableName}'", tableName);
                        return false;
                    }
                    _logger.LogInformation("Table '{TableName}' deleted successfully", tableName);
                }

                // Create the table
                _logger.LogInformation("Creating table '{TableName}'", tableName);
                var createSuccess = await CreateTableUsingManager(tableManager);
                if (!createSuccess)
                {
                    _logger.LogError("Failed to create table '{TableName}'", tableName);
                    return false;
                }

                _logger.LogInformation("Table '{TableName}' created successfully", tableName);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing table for model type {ModelType}", modelType.Name);
                return false;
            }
        }

        /// <summary>
        /// Validates a table for a specific model type.
        /// </summary>
        /// <param name="modelType">Model type to validate</param>
        /// <returns>Task representing the validation result</returns>
        public async Task<TableValidationResult> ValidateTableAsync(Type modelType)
        {
            _logger.LogDebug("Validating table for model type {ModelType}", modelType.Name);

            try
            {
                var tableManager = CreateTableManager(modelType);
                if (tableManager == null)
                {
                    return new TableValidationResult
                    {
                        IsValid = false,
                        ValidationErrors = new List<string> { $"Failed to create table manager for model type {modelType.Name}" }
                    };
                }

                return await ValidateTableUsingManager(tableManager);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating table for model type {ModelType}", modelType.Name);
                return new TableValidationResult
                {
                    IsValid = false,
                    ValidationErrors = new List<string> { $"Exception during validation: {ex.Message}" }
                };
            }
        }

        /// <summary>
        /// Creates a table manager instance for the specified model type using reflection.
        /// </summary>
        /// <param name="modelType">Model type</param>
        /// <returns>Table manager instance or null if creation failed</returns>
        private object? CreateTableManager(Type modelType)
        {
            try
            {
                var tableManagerType = typeof(DynamoDBTableManager<>).MakeGenericType(modelType);
                return Activator.CreateInstance(tableManagerType, _dynamoClient);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create table manager for model type {ModelType}", modelType.Name);
                return null;
            }
        }

        /// <summary>
        /// Gets the table name for a model type using reflection.
        /// </summary>
        /// <param name="modelType">Model type</param>
        /// <returns>Table name</returns>
        private string GetTableName(Type modelType)
        {
            try
            {
                var method = modelType.GetMethod("GetTableName", BindingFlags.Public | BindingFlags.Static);
                if (method != null)
                {
                    return (string)method.Invoke(null, null)!;
                }

                // Fallback to type name
                return modelType.Name;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to get table name for model type {ModelType}, using type name", modelType.Name);
                return modelType.Name;
            }
        }

        /// <summary>
        /// Validates a table using the table manager instance.
        /// </summary>
        /// <param name="tableManager">Table manager instance</param>
        /// <returns>Validation result</returns>
        private async Task<TableValidationResult> ValidateTableUsingManager(object tableManager)
        {
            var method = tableManager.GetType().GetMethod("ValidateTableAsync");
            if (method == null)
            {
                throw new InvalidOperationException("ValidateTableAsync method not found on table manager");
            }

            var task = (Task<TableValidationResult>)method.Invoke(tableManager, null)!;
            return await task;
        }

        /// <summary>
        /// Checks if a table exists using the table manager instance.
        /// </summary>
        /// <param name="tableManager">Table manager instance</param>
        /// <returns>True if table exists</returns>
        private async Task<bool> TableExistsUsingManager(object tableManager)
        {
            var method = tableManager.GetType().GetMethod("TableExistsAsync");
            if (method == null)
            {
                throw new InvalidOperationException("TableExistsAsync method not found on table manager");
            }

            var task = (Task<bool>)method.Invoke(tableManager, null)!;
            return await task;
        }

        /// <summary>
        /// Deletes a table using the table manager instance.
        /// </summary>
        /// <param name="tableManager">Table manager instance</param>
        /// <returns>True if deletion was successful</returns>
        private async Task<bool> DeleteTableUsingManager(object tableManager)
        {
            var method = tableManager.GetType().GetMethod("DeleteTableAsync");
            if (method == null)
            {
                throw new InvalidOperationException("DeleteTableAsync method not found on table manager");
            }

            var task = (Task<bool>)method.Invoke(tableManager, null)!;
            return await task;
        }

        /// <summary>
        /// Creates a table using the table manager instance.
        /// </summary>
        /// <param name="tableManager">Table manager instance</param>
        /// <returns>True if creation was successful</returns>
        private async Task<bool> CreateTableUsingManager(object tableManager)
        {
            var method = tableManager.GetType().GetMethod("CreateTableAsync");
            if (method == null)
            {
                throw new InvalidOperationException("CreateTableAsync method not found on table manager");
            }

            var task = (Task<bool>)method.Invoke(tableManager, null)!;
            return await task;
        }
    }
}
